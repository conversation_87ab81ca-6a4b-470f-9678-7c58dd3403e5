'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import BPlusTreeVisualizer, { BPlusTreeVisualizerRef } from '@/components/BPlusXyflow/BPlusTreeVisualizer';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Stack,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';
import { BPlusTreeAlgorithm } from '@/lib/bplus-tree/algorithm';
import { AnimationManager } from '@/lib/bplus-tree/animationManager';

const BPlusTestPage: React.FC = () => {
  // 基础配置
  const [order] = useState<number>(3);
  const [initialKeys] = useState<number[]>([1, 2, 3, 4, 5]);

  // 外部控件状态
  const [insertValue, setInsertValue] = useState<string>('');
  const [deleteValue, setDeleteValue] = useState<string>('');

  // 可视化状态
  const [currentNodes, setCurrentNodes] = useState<Node<BPlusNodeData>[]>([]);
  const [currentEdges, setCurrentEdges] = useState<Edge[]>([]);

  // 核心实例引用 - 提升到父组件
  const bPlusTreeAlgorithmRef = useRef<BPlusTreeAlgorithm | null>(null);
  const animationManagerRef = useRef<AnimationManager | null>(null);
  const visualizerRef = useRef<BPlusTreeVisualizerRef | null>(null);

  // 初始化核心实例
  useEffect(() => {
    bPlusTreeAlgorithmRef.current = new BPlusTreeAlgorithm(order);
    animationManagerRef.current = new AnimationManager();
  }, [order]);

  // 处理状态变更回调
  const handleStateChange = useCallback((state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => {
    setCurrentNodes(state.nodes);
    setCurrentEdges(state.edges);
    console.log('状态更新:', state.operation, state.operationKey, '当前键值:', state.keys);
  }, []);

  // 外部插入事件处理器
  const handleExternalInsert = useCallback(async () => {
    if (!insertValue || !visualizerRef.current) return;

    const key = parseInt(insertValue, 10);
    if (isNaN(key)) {
      console.error('插入值必须是有效数字');
      return;
    }

    try {
      await visualizerRef.current.runInsertAnimation(key);
      setInsertValue(''); // 清空输入框
      console.log(`成功插入键值: ${key}`);
    } catch (error) {
      console.error('插入失败:', error);
    }
  }, [insertValue]);

  // 外部删除事件处理器
  const handleExternalDelete = useCallback(async () => {
    if (!deleteValue || !visualizerRef.current) return;

    const key = parseInt(deleteValue, 10);
    if (isNaN(key)) {
      console.error('删除值必须是有效数字');
      return;
    }

    try {
      await visualizerRef.current.runDeleteAnimation(key);
      setDeleteValue(''); // 清空输入框
      console.log(`成功删除键值: ${key}`);
    } catch (error) {
      console.error('删除失败:', error);
    }
  }, [deleteValue]);

  // 外部重置事件处理器
  const handleExternalReset = useCallback(async () => {
    if (!visualizerRef.current) return;

    try {
      await visualizerRef.current.resetTree();
      console.log('成功重置树');
    } catch (error) {
      console.error('重置失败:', error);
    }
  }, []);

  return (
    <Box sx={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#f4f6f8' }}>
      {/* 标题栏 */}
      <Paper sx={{ p: 2, m: 2, mb: 1 }}>
        <Typography variant="h5" component="h1" sx={{ color: 'var(--secondary-text)' }}>
          B+ 树测试页面 (阶数: {order})
        </Typography>
      </Paper>

      {/* 主要内容区域 */}
      <Box sx={{ flex: 1, display: 'flex', gap: 2, m: 2, mt: 0 }}>
        {/* 左侧控制面板 */}
        <Paper sx={{ width: 300, p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h6" sx={{ color: 'var(--secondary-text)' }}>
            操作控制
          </Typography>

          <Divider />

          {/* 插入操作 */}
          <Stack spacing={2}>
            <Typography variant="subtitle2" sx={{ color: 'var(--secondary-text)' }}>
              插入键值
            </Typography>
            <TextField
              size="small"
              type="number"
              value={insertValue}
              onChange={(e) => setInsertValue(e.target.value)}
              placeholder="输入要插入的数字"
              fullWidth
            />
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleExternalInsert}
              disabled={!insertValue}
              fullWidth
            >
              插入
            </Button>
          </Stack>

          <Divider />

          {/* 删除操作 */}
          <Stack spacing={2}>
            <Typography variant="subtitle2" sx={{ color: 'var(--secondary-text)' }}>
              删除键值
            </Typography>
            <TextField
              size="small"
              type="number"
              value={deleteValue}
              onChange={(e) => setDeleteValue(e.target.value)}
              placeholder="输入要删除的数字"
              fullWidth
            />
            <Button
              variant="contained"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleExternalDelete}
              disabled={!deleteValue}
              fullWidth
            >
              删除
            </Button>
          </Stack>

          <Divider />

          {/* 重置操作 */}
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleExternalReset}
            fullWidth
          >
            重置树
          </Button>
        </Paper>

        {/* 右侧可视化区域 */}
        <Box sx={{ flex: 1, position: 'relative' }}>
          <BPlusTreeVisualizer
            ref={visualizerRef}
            initialKeys={initialKeys}
            order={order}
            externalNodes={currentNodes}
            externalEdges={currentEdges}
            onStateChange={handleStateChange}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default BPlusTestPage;