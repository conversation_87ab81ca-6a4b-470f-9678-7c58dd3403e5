'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Box } from '@mui/material';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import styles from './page.module.css';
import BPlusHistoryPanel from '@/components/BPlusHistory/BPlusHistoryPanel';
import BPlusTreeWorkspace, { BPlusTreeWorkspaceRef } from '@/components/BPlusXyflow/BPlusTreeWorkspace';
import BPlusSidebar from '@/components/BPlusHistory/BPlusSidebar';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';

// 布局算法
const layoutNodes = (nodes: Node<BPlusNodeData>[], edges: Edge[]): Node<BPlusNodeData>[] => {
  if (nodes.length === 0) return nodes;

  const levelGroups: { [level: number]: Node<BPlusNodeData>[] } = {};
  nodes.forEach(node => {
    const level = node.data.level;
    if (!levelGroups[level]) levelGroups[level] = [];
    levelGroups[level].push(node);
  });

  const layoutedNodes: Node<BPlusNodeData>[] = [];
  const levels = Object.keys(levelGroups).map(Number).sort((a, b) => b - a);

  levels.forEach((level, levelIndex) => {
    const nodesInLevel = levelGroups[level];
    const y = levelIndex * 150 + 50;
    const totalWidth = (nodesInLevel.length - 1) * 200;
    const startX = -totalWidth / 2;

    nodesInLevel.forEach((node, nodeIndex) => {
      layoutedNodes.push({
        ...node,
        position: {
          x: startX + nodeIndex * 200,
          y: y
        }
      });
    });
  });

  return layoutedNodes;
};

const BPlusPage: React.FC = () => {
  // 状态管理
  const [selectedSession, setSelectedSession] = useState<HistorySession | null>(null);
  const [selectedStep, setSelectedStep] = useState<HistoryStep | null>(null);
  const [currentNodes, setCurrentNodes] = useState<Node<BPlusNodeData>[]>([]);
  const [currentEdges, setCurrentEdges] = useState<Edge[]>([]);
  const [isHistoryCollapsed, setIsHistoryCollapsed] = useState<boolean>(false);

  // 工作区组件的 ref，用于访问内部的可视化组件
  const workspaceRef = useRef<BPlusTreeWorkspaceRef | null>(null);

  // 调试：检查 workspace ref 的状态
  useEffect(() => {
    console.log('🔗 Workspace ref 状态:', workspaceRef.current ? '已初始化' : '未初始化');
    if (workspaceRef.current) {
      const visualizerRef = workspaceRef.current.getVisualizerRef();
      console.log('🔗 Visualizer ref 状态:', visualizerRef ? '已初始化' : '未初始化');
    }
  });

  // 处理B+树状态变更：更新显示状态并自动保存到历史记录中，支持操作回溯功能
  const handleStateChange = useCallback(async (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => {
    // 更新当前显示状态
    setCurrentNodes(state.nodes);
    setCurrentEdges(state.edges);

    // 如果有选中的会话且有操作，保存到历史记录
    if (selectedSession && state.operation) {
      try {
        const storage = await getBPlusHistoryStorage();

        // 创建新的历史步骤
        const newStep: HistoryStep = {
          id: `step-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          operation: state.operation,
          key: state.operationKey,
          timestamp: Date.now(),
          nodes: state.nodes,
          edges: state.edges,
          keys: state.keys,
          description: generateStepDescription(state.operation, state.operationKey)
        };

        // 添加步骤到会话
        const result = await storage.addStep(selectedSession.id, newStep);

        if (result.success) {
          // 更新选中的会话和步骤
          setSelectedSession(result.data);
          setSelectedStep(newStep);
          console.log(`已记录操作: ${newStep.description}`);
        } else {
          console.error('记录操作失败:', result.error);
        }
      } catch (err) {
        console.error('记录操作时发生错误:', err);
      }
    }
  }, [selectedSession]);



  // 修复后的事件处理器：直接调用可视化组件的方法，让其内部处理算法逻辑
  const handleInsert = useCallback(async (key: number) => {
    console.log('🚀 页面级 handleInsert 被调用, key:', key);
    // 获取内部的 visualizer ref
    const internalVisualizerRef = workspaceRef.current?.getVisualizerRef();
    if (!internalVisualizerRef) {
      console.error('❌ 可视化组件未初始化');
      return;
    }

    try {
      console.log('📞 调用 visualizer.insertKey...');
      // 直接调用可视化组件的插入方法，让其内部处理算法逻辑
      await internalVisualizerRef.insertKey(key);
      console.log(`✅ 成功插入键值 ${key}`);
    } catch (error) {
      console.error('❌ 插入操作失败:', error);
      throw error;
    }
  }, []);

  const handleDelete = useCallback(async (key: number) => {
    // 获取内部的 visualizer ref
    const internalVisualizerRef = workspaceRef.current?.getVisualizerRef();
    if (!internalVisualizerRef) {
      console.error('可视化组件未初始化');
      return;
    }

    try {
      // 直接调用可视化组件的删除方法
      await internalVisualizerRef.deleteKey(key);
      console.log(`成功删除键值 ${key}`);
    } catch (error) {
      console.error('删除操作失败:', error);
      throw error;
    }
  }, []);

  const handleReset = useCallback(async () => {
    // 获取内部的 visualizer ref
    const internalVisualizerRef = workspaceRef.current?.getVisualizerRef();
    if (!internalVisualizerRef) {
      console.error('可视化组件未初始化');
      return;
    }

    try {
      // 直接调用可视化组件的重置方法
      await internalVisualizerRef.resetTree();
      console.log('B+树已重置');
    } catch (error) {
      console.error('重置操作失败:', error);
      throw error;
    }
  }, []);

  // 测试函数：直接测试插入功能
  const testInsert = useCallback(async () => {
    console.log('🧪 开始测试插入功能...');
    try {
      await handleInsert(5);
      console.log('🧪 测试插入完成');
    } catch (error) {
      console.error('🧪 测试插入失败:', error);
    }
  }, [handleInsert]);

  // 从历史步骤重建算法状态（现在通过可视化组件处理）
  const rebuildAlgorithmFromStep = useCallback((step: HistoryStep) => {
    // 获取内部的 visualizer ref
    const internalVisualizerRef = workspaceRef.current?.getVisualizerRef();
    if (!internalVisualizerRef) {
      console.warn('可视化组件未初始化，无法重建算法状态');
      return;
    }

    try {
      // 通过可视化组件重置并重建状态
      // 这里我们依赖外部节点数据的传递来重建状态
      console.log(`准备重建算法状态，包含键值: [${step.keys?.join(', ') || ''}]`);
    } catch (error) {
      console.error('重建算法状态失败:', error);
    }
  }, []);

  // 处理会话选择
  const handleSessionSelect = useCallback((session: HistorySession) => {
    setSelectedSession(session);
    // 自动选择当前步骤
    if (session.steps.length > 0 && session.currentStepIndex >= 0) {
      const currentStep = session.steps[session.currentStepIndex];
      setSelectedStep(currentStep);
      setCurrentNodes(currentStep.nodes);
      setCurrentEdges(currentStep.edges);
      // 重建算法状态
      rebuildAlgorithmFromStep(currentStep);
    } else {
      setSelectedStep(null);
      setCurrentNodes([]);
      setCurrentEdges([]);
      // 算法状态由可视化组件内部管理，这里不需要手动清空
    }
  }, [rebuildAlgorithmFromStep]);

  // 处理步骤选择
  const handleStepSelect = useCallback((step: HistoryStep, stepIndex: number) => {
    setSelectedStep(step);
    setCurrentNodes(step.nodes);
    setCurrentEdges(step.edges);
    // 重建算法状态
    rebuildAlgorithmFromStep(step);
  }, [rebuildAlgorithmFromStep]);

  // 生成步骤描述
  const generateStepDescription = (operation: string, key?: number): string => {
    switch (operation) {
      case 'insert':
        return `插入键值 ${key}`;
      case 'delete':
        return `删除键值 ${key}`;
      case 'reset':
        return '重置B+树';
      default:
        return '未知操作';
    }
  };

  // 处理历史面板切换
  const handleToggleHistory = useCallback(() => {
    setIsHistoryCollapsed(prev => !prev);
  }, []);

  // 处理新会话创建
  const handleSessionCreated = useCallback((session: HistorySession) => {
    setSelectedSession(session);
    setSelectedStep(null);
    setCurrentNodes([]);
    setCurrentEdges([]);
  }, []);

  return (
    <div className={styles.pageContainer}>
      <div className={styles.contentContainer}>
        <div style={{ display: 'flex', height: '100%' }}>
          {/* 左侧边栏 */}
          <div style={{ width: '50px', flexShrink: 0 }}>
            <BPlusSidebar
              isHistoryCollapsed={isHistoryCollapsed}
              onToggleHistory={handleToggleHistory}
              onSessionCreated={handleSessionCreated}
            />
          </div>

          {/* 主内容区域 */}
          <div style={{ flex: 1, display: 'flex' }}>
            <PanelGroup direction="horizontal">
              {/* 历史管理面板 */}
              {!isHistoryCollapsed && (
                <Panel
                  defaultSize={25}
                  minSize={20}
                  maxSize={40}
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    backgroundColor: 'var(--card-bg)',
                    borderRight: '1px solid var(--card-border)'
                  }}
                >
                  <BPlusHistoryPanel
                    selectedSession={selectedSession}
                    selectedStep={selectedStep}
                    onSessionSelect={handleSessionSelect}
                    onStepSelect={handleStepSelect}
                  />
                </Panel>
              )}

          {/* 拖拽手柄 */}
          <PanelResizeHandle 
            style={{
              width: '6px',
              backgroundColor: 'var(--card-border)',
              cursor: 'col-resize',
              transition: 'background-color 0.2s',
              position: 'relative'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '3px',
                height: '40px',
                backgroundColor: 'var(--secondary-text)',
                borderRadius: '2px',
                opacity: 0.3,
                '&:hover': {
                  opacity: 0.6
                }
              }}
            />
          </PanelResizeHandle>

          {/* 右侧：B+树操作区域 */}
          <Panel 
            defaultSize={75}
            minSize={60}
            style={{
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: 'var(--background)'
            }}
          >
            <BPlusTreeWorkspace
              ref={workspaceRef}
              selectedSession={selectedSession}
              currentNodes={currentNodes}
              currentEdges={currentEdges}
              onStateChange={handleStateChange}
              onInsert={handleInsert}
              onDelete={handleDelete}
              onReset={handleReset}
            />
          </Panel>
        </PanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BPlusPage;
