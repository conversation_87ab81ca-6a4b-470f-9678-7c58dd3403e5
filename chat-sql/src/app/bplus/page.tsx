'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Box } from '@mui/material';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import styles from './page.module.css';
import BPlusHistoryPanel from '@/components/BPlusHistory/BPlusHistoryPanel';
import BPlusTreeWorkspace, { BPlusTreeWorkspaceRef } from '@/components/BPlusXyflow/BPlusTreeWorkspace';
import BPlusSidebar from '@/components/BPlusHistory/BPlusSidebar';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';
import { BPlusTreeAlgorithm } from '@/lib/bplus-tree/algorithm';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';

// 布局算法
const layoutNodes = (nodes: Node<BPlusNodeData>[], edges: Edge[]): Node<BPlusNodeData>[] => {
  if (nodes.length === 0) return nodes;

  const levelGroups: { [level: number]: Node<BPlusNodeData>[] } = {};
  nodes.forEach(node => {
    const level = node.data.level;
    if (!levelGroups[level]) levelGroups[level] = [];
    levelGroups[level].push(node);
  });

  const layoutedNodes: Node<BPlusNodeData>[] = [];
  const levels = Object.keys(levelGroups).map(Number).sort((a, b) => b - a);

  levels.forEach((level, levelIndex) => {
    const nodesInLevel = levelGroups[level];
    const y = levelIndex * 150 + 50;
    const totalWidth = (nodesInLevel.length - 1) * 200;
    const startX = -totalWidth / 2;

    nodesInLevel.forEach((node, nodeIndex) => {
      layoutedNodes.push({
        ...node,
        position: {
          x: startX + nodeIndex * 200,
          y: y
        }
      });
    });
  });

  return layoutedNodes;
};

const BPlusPage: React.FC = () => {
  // 状态管理
  const [selectedSession, setSelectedSession] = useState<HistorySession | null>(null);
  const [selectedStep, setSelectedStep] = useState<HistoryStep | null>(null);
  const [currentNodes, setCurrentNodes] = useState<Node<BPlusNodeData>[]>([]);
  const [currentEdges, setCurrentEdges] = useState<Edge[]>([]);
  const [isHistoryCollapsed, setIsHistoryCollapsed] = useState<boolean>(false);

  // B+树算法实例
  const bPlusTreeRef = useRef<BPlusTreeAlgorithm | null>(null);
  // BPlusTreeWorkspace 组件的 ref
  const workspaceRef = useRef<BPlusTreeWorkspaceRef | null>(null);

  // 初始化B+树算法实例
  useEffect(() => {
    const order = selectedSession?.order || 3;
    bPlusTreeRef.current = new BPlusTreeAlgorithm(order);
  }, [selectedSession?.order]);

  // 处理B+树状态变更：更新显示状态并自动保存到历史记录中，支持操作回溯功能
  const handleStateChange = useCallback(async (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => {
    // 更新当前显示状态
    setCurrentNodes(state.nodes);
    setCurrentEdges(state.edges);

    // 如果有选中的会话且有操作，保存到历史记录
    if (selectedSession && state.operation) {
      try {
        const storage = await getBPlusHistoryStorage();

        // 创建新的历史步骤
        const newStep: HistoryStep = {
          id: `step-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          operation: state.operation,
          key: state.operationKey,
          timestamp: Date.now(),
          nodes: state.nodes,
          edges: state.edges,
          keys: state.keys,
          description: generateStepDescription(state.operation, state.operationKey)
        };

        // 添加步骤到会话
        const result = await storage.addStep(selectedSession.id, newStep);

        if (result.success) {
          // 更新选中的会话和步骤
          setSelectedSession(result.data);
          setSelectedStep(newStep);
          console.log(`已记录操作: ${newStep.description}`);
        } else {
          console.error('记录操作失败:', result.error);
        }
      } catch (err) {
        console.error('记录操作时发生错误:', err);
      }
    }
  }, [selectedSession]);

  // 更新视图的辅助函数
  const updateTreeView = useCallback((operation?: 'insert' | 'delete' | 'reset', operationKey?: number) => {
    if (!bPlusTreeRef.current) return;

    try {
      // 简化处理：直接触发状态变更，让可视化组件自己处理更新
      const keys = bPlusTreeRef.current.getAllKeys();
      handleStateChange({
        nodes: currentNodes, // 使用当前节点，让可视化组件自己更新
        edges: currentEdges, // 使用当前边，让可视化组件自己更新
        keys,
        operation,
        operationKey
      });
    } catch (error) {
      console.error('Error updating tree view:', error);
    }
  }, [handleStateChange, currentNodes, currentEdges]);

  // B+树操作回调 - 通过 workspace ref 调用可视化组件
  const handleInsert = useCallback(async (key: number) => {
    if (workspaceRef.current) {
      try {
        await workspaceRef.current.insertKey(key);
      } catch (error) {
        console.error('插入操作失败:', error);
        throw error;
      }
    }
  }, []);

  const handleDelete = useCallback(async (key: number) => {
    if (workspaceRef.current) {
      try {
        await workspaceRef.current.deleteKey(key);
      } catch (error) {
        console.error('删除操作失败:', error);
        throw error;
      }
    }
  }, []);

  const handleReset = useCallback(async () => {
    if (workspaceRef.current) {
      try {
        await workspaceRef.current.resetTree();
      } catch (error) {
        console.error('重置操作失败:', error);
        throw error;
      }
    }
  }, []);

  // 从历史步骤重建算法状态
  const rebuildAlgorithmFromStep = useCallback((step: HistoryStep) => {
    if (!bPlusTreeRef.current) return;

    try {
      // 清空当前算法状态
      bPlusTreeRef.current.clear();

      // 从步骤的键值数组重建树结构
      if (step.keys && step.keys.length > 0) {
        // 按顺序插入所有键值来重建树结构
        step.keys.forEach(key => {
          try {
            if (!bPlusTreeRef.current!.find(key)) {
              bPlusTreeRef.current!.insertElement(key);
            }
          } catch (error) {
            console.warn(`Failed to rebuild key ${key}:`, error);
          }
        });
      }

      console.log(`算法状态已重建，包含键值: [${step.keys?.join(', ') || ''}]`);
    } catch (error) {
      console.error('重建算法状态失败:', error);
    }
  }, []);

  // 处理会话选择
  const handleSessionSelect = useCallback((session: HistorySession) => {
    setSelectedSession(session);
    // 自动选择当前步骤
    if (session.steps.length > 0 && session.currentStepIndex >= 0) {
      const currentStep = session.steps[session.currentStepIndex];
      setSelectedStep(currentStep);
      setCurrentNodes(currentStep.nodes);
      setCurrentEdges(currentStep.edges);
      // 重建算法状态
      rebuildAlgorithmFromStep(currentStep);
    } else {
      setSelectedStep(null);
      setCurrentNodes([]);
      setCurrentEdges([]);
      // 清空算法状态
      if (bPlusTreeRef.current) {
        bPlusTreeRef.current.clear();
      }
    }
  }, [rebuildAlgorithmFromStep]);

  // 处理步骤选择
  const handleStepSelect = useCallback((step: HistoryStep, stepIndex: number) => {
    setSelectedStep(step);
    setCurrentNodes(step.nodes);
    setCurrentEdges(step.edges);
    // 重建算法状态
    rebuildAlgorithmFromStep(step);
  }, [rebuildAlgorithmFromStep]);

  // 生成步骤描述
  const generateStepDescription = (operation: string, key?: number): string => {
    switch (operation) {
      case 'insert':
        return `插入键值 ${key}`;
      case 'delete':
        return `删除键值 ${key}`;
      case 'reset':
        return '重置B+树';
      default:
        return '未知操作';
    }
  };

  // 处理历史面板切换
  const handleToggleHistory = useCallback(() => {
    setIsHistoryCollapsed(prev => !prev);
  }, []);

  // 处理新会话创建
  const handleSessionCreated = useCallback((session: HistorySession) => {
    setSelectedSession(session);
    setSelectedStep(null);
    setCurrentNodes([]);
    setCurrentEdges([]);
  }, []);

  return (
    <div className={styles.pageContainer}>
      <div className={styles.contentContainer}>
        <div style={{ display: 'flex', height: '100%' }}>
          {/* 左侧边栏 */}
          <div style={{ width: '50px', flexShrink: 0 }}>
            <BPlusSidebar
              isHistoryCollapsed={isHistoryCollapsed}
              onToggleHistory={handleToggleHistory}
              onSessionCreated={handleSessionCreated}
            />
          </div>

          {/* 主内容区域 */}
          <div style={{ flex: 1, display: 'flex' }}>
            <PanelGroup direction="horizontal">
              {/* 历史管理面板 */}
              {!isHistoryCollapsed && (
                <Panel
                  defaultSize={25}
                  minSize={20}
                  maxSize={40}
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    backgroundColor: 'var(--card-bg)',
                    borderRight: '1px solid var(--card-border)'
                  }}
                >
                  <BPlusHistoryPanel
                    selectedSession={selectedSession}
                    selectedStep={selectedStep}
                    onSessionSelect={handleSessionSelect}
                    onStepSelect={handleStepSelect}
                  />
                </Panel>
              )}

          {/* 拖拽手柄 */}
          <PanelResizeHandle 
            style={{
              width: '6px',
              backgroundColor: 'var(--card-border)',
              cursor: 'col-resize',
              transition: 'background-color 0.2s',
              position: 'relative'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '3px',
                height: '40px',
                backgroundColor: 'var(--secondary-text)',
                borderRadius: '2px',
                opacity: 0.3,
                '&:hover': {
                  opacity: 0.6
                }
              }}
            />
          </PanelResizeHandle>

          {/* 右侧：B+树操作区域 */}
          <Panel 
            defaultSize={75}
            minSize={60}
            style={{
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: 'var(--background)'
            }}
          >
            <BPlusTreeWorkspace
              ref={workspaceRef}
              selectedSession={selectedSession}
              currentNodes={currentNodes}
              currentEdges={currentEdges}
              onStateChange={handleStateChange}
              onInsert={handleInsert}
              onDelete={handleDelete}
              onReset={handleReset}
            />
          </Panel>
        </PanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BPlusPage;
