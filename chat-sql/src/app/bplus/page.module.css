.pageContainer {
  height: calc(100vh - var(--navbar-height));
  margin-top: var(--navbar-height);
  background: var(--background);
  overflow: hidden;
}

.contentContainer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.mainContainer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
}

.historyPanel {
  background: var(--card-bg);
  border-right: 1px solid var(--card-border);
  min-width: 300px;
  max-width: 500px;
}

.workspacePanel {
  background: var(--background);
  flex: 1;
  min-width: 600px;
}

/* 暗色主题支持 */
[data-theme="dark"] .historyPanel {
  background: var(--card-bg);
  border-right-color: var(--card-border);
}

[data-theme="dark"] .workspacePanel {
  background: var(--background);
}
