/**
 * B+树页面侧边栏组件
 * 参考ER图页面和主页面的侧边栏实现，提供一致的视觉风格和功能
 */

import React, { useState } from 'react';
import { Button, Tooltip, Modal } from 'antd';
import {
  PlusCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  QuestionCircleOutlined,
  GithubOutlined
} from '@ant-design/icons';
import { HistorySession } from '@/types/bPlusHistory';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';

interface BPlusSidebarProps {
  isHistoryCollapsed: boolean;
  onToggleHistory: () => void;
  onSessionCreated: (session: HistorySession) => void;
}

const BPlusSidebar: React.FC<BPlusSidebarProps> = ({
  isHistoryCollapsed,
  onToggleHistory,
  onSessionCreated
}) => {
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // 新建B+树会话
  const handleNewSession = async () => {
    if (isCreating) return;
    
    setIsCreating(true);
    try {
      const storage = await getBPlusHistoryStorage();
      const sessionName = `B+树会话-${new Date().toLocaleString()}`;
      const result = await storage.createSession(sessionName, 3); // 默认3阶
      
      if (result.success) {
        onSessionCreated(result.data);
      } else {
        console.error('创建会话失败:', result.error);
      }
    } catch (error) {
      console.error('创建会话时发生错误:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // 打开帮助模态框
  const handleHelpClick = () => {
    setIsHelpModalOpen(true);
  };

  // 打开GitHub仓库
  const handleGithubClick = () => {
    window.open('https://github.com/ffy6511/chatSQL', '_blank');
  };

  return (
    <div className="global-sidebar-container">
      {/* 顶部按钮区域 */}
      <div className="global-sidebar-top-buttons">
        <Tooltip title="新建B+树会话" placement="right">
          <Button 
            type="text"
            icon={<PlusCircleOutlined />}
            className="global-sidebar-action-button"
            onClick={handleNewSession}
            loading={isCreating}
          />
        </Tooltip>
        
        <Tooltip title={isHistoryCollapsed ? "展开历史记录" : "收起历史记录"} placement="right">
          <Button
            type="text"
            icon={isHistoryCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            className="global-sidebar-action-button"
            onClick={onToggleHistory}
          />
        </Tooltip>
      </div>

      {/* 中间菜单容器 */}
      <div className="global-sidebar-menu-container">
        {/* 预留空间，可以添加其他功能按钮 */}
      </div>
      
      {/* 底部按钮区域 */}
      <div className="global-sidebar-bottom-buttons">
        <Tooltip title="帮助" placement="right">
          <Button 
            type="text" 
            icon={<QuestionCircleOutlined />}
            className="global-sidebar-action-button"
            onClick={handleHelpClick}
          />
        </Tooltip>
        
        <Tooltip title="GitHub仓库" placement="right">
          <Button 
            type="text" 
            icon={<GithubOutlined />}
            className="global-sidebar-action-button"
            onClick={handleGithubClick}
          />
        </Tooltip>
      </div>

      {/* 帮助模态框 */}
      <Modal
        title="B+树可视化工具帮助"
        open={isHelpModalOpen}
        onCancel={() => setIsHelpModalOpen(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '16px 0' }}>
          <h3>B+树可视化与历史管理工具</h3>
          <p>这是一个交互式的B+树可视化工具，支持动画演示和历史记录管理。</p>
          
          <h4>主要功能：</h4>
          <ul>
            <li><strong>B+树操作</strong>：支持插入、删除、重置等基本操作</li>
            <li><strong>动画演示</strong>：可视化展示B+树的分裂、合并过程</li>
            <li><strong>历史管理</strong>：记录所有操作步骤，支持回溯查看</li>
            <li><strong>批量操作</strong>：支持空格分隔的多个数值批量处理</li>
            <li><strong>会话管理</strong>：创建多个独立的B+树会话</li>
          </ul>

          <h4>使用方法：</h4>
          <ol>
            <li>点击"新建会话"创建B+树操作会话</li>
            <li>在操作面板中输入数值进行插入或删除</li>
            <li>支持批量操作：输入"1 2 3 4 5"可一次插入多个值</li>
            <li>在历史面板中查看操作记录和回溯状态</li>
            <li>使用缩放控件调整视图大小和位置</li>
          </ol>

          <h4>快捷键：</h4>
          <ul>
            <li><strong>Enter</strong>：在输入框中按回车执行操作</li>
            <li><strong>鼠标滚轮</strong>：缩放B+树视图</li>
            <li><strong>拖拽</strong>：移动B+树视图位置</li>
          </ul>
        </div>
      </Modal>
    </div>
  );
};

export default BPlusSidebar;
