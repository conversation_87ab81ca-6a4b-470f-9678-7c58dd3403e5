/**
 * 重构后的B+树可视化组件
 * 基于指令序列的动画系统，实现算法与可视化的完全分离
 */

import React, { useEffect, useState, useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Node,
  Edge,
  BackgroundVariant,
  ReactFlowProvider,
  Panel,
  MarkerType
} from '@xyflow/react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  Snackbar,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  FolderOpen as LoadIcon,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  RedoOutlined
} from '@mui/icons-material';
import { Tooltip } from '@mui/material';
import { useReactFlow } from '@xyflow/react';

// 导入新的重构组件
import { BPlusTreeAlgorithm } from '../../lib/bplus-tree/algorithm';
import { AnimationManager, AnimationState } from '../../lib/bplus-tree/animationManager';
import { CommandExecutor } from '../../lib/bplus-tree/commandExecutor';
import { BPlusCommand } from '../../lib/bplus-tree/commands';
import { getBPlusTreeStorage, BPlusTreeStorage } from '../../lib/bplus-tree/storage';
import AnimationControls from './AnimationControls';
import BPlusInternalNode from './BPlusInternalNode';
import BPlusLeafNode from './BPlusLeafNode';
import { BPlusNodeData } from '../utils/bPlusTreeToReactFlow';
import styles from './BPlusTreeVisualizer.module.css';
import '@xyflow/react/dist/style.css';

// 自定义节点类型
const nodeTypes = {
  bPlusInternalNode: BPlusInternalNode,
  bPlusLeafNode: BPlusLeafNode,
};

// 布局算法 
const layoutNodes = (nodes: Node<BPlusNodeData>[], edges: Edge[]): Node<BPlusNodeData>[] => {
  if (nodes.length === 0) return nodes;

  const levelGroups: { [level: number]: Node<BPlusNodeData>[] } = {};
  nodes.forEach(node => {
    const level = node.data.level;
    if (!levelGroups[level]) levelGroups[level] = [];
    levelGroups[level].push(node);
  });

  const layoutedNodes: Node<BPlusNodeData>[] = [];
  // JS对象的键自动被转化为字符串存储, 因此使用 .map(Number) 转换回数字
  const levels = Object.keys(levelGroups).map(Number).sort((a, b) => b - a);

  levels.forEach((level, levelIndex) => {
    const nodesInLevel = levelGroups[level];

    nodesInLevel.sort((a, b) => {
      // 如果不存在key, 使用Infinity 默认放到最后
      const firstKeyA = a.data.keys.find(k => k !== null) as number | undefined ?? Infinity;
      const firstKeyB = b.data.keys.find(k => k !== null) as number | undefined ?? Infinity;
      return firstKeyA - firstKeyB;
    });

    const logicalSlotWidth = 200; // 为每个节点分配一个固定的"逻辑槽位"宽度
    const levelWidth = nodesInLevel.length * logicalSlotWidth;
    const startX = -levelWidth / 2;

    nodesInLevel.forEach((node, index) => {
      // 计算每个节点在自己的逻辑槽位中的中心X坐标
      const x = startX + index * logicalSlotWidth + logicalSlotWidth / 2;
      const y = levelIndex * 120;

      layoutedNodes.push({
        ...node,
        position: { x, y }
      });
    });
  });

  return layoutedNodes;
};

// 自定义横向排列的操作按钮组件
const CustomZoomControls: React.FC<{ onReset: () => void }> = ({ onReset }) => {
  const { zoomIn, zoomOut, fitView } = useReactFlow();
  return (
    <Panel position="bottom-right" className={styles['bplus-custom-controls']}>
      <Tooltip title="放大" placement="top">
        <button
          type="button"
          className={styles['bplus-zoom-button']}
          onClick={() => zoomIn({ duration: 800 })}
          aria-label="放大"
        >
          <ZoomInOutlined />
        </button>
      </Tooltip>
      <Tooltip title="缩小" placement="top">
        <button
          type="button"
          className={styles['bplus-zoom-button']}
          onClick={() => zoomOut({ duration: 800 })}
          aria-label="缩小"
        >
          <ZoomOutOutlined />
        </button>
      </Tooltip>
      <Tooltip title="适应视图" placement="top">
        <button
          type="button"
          className={styles['bplus-zoom-button'] + ' ' + styles['bplus-fit-button']}
          onClick={() => fitView({ duration: 800, padding: 0.2 })}
          aria-label="适应视图"
        >
          <FullscreenOutlined />
        </button>
      </Tooltip>
      <Tooltip title="重置为初始状态" placement="top">
        <button
          type="button"
          className={styles['bplus-zoom-button'] + ' ' + styles['bplus-reset-button']}
          onClick={onReset}
          aria-label="重置为初始状态"
        >
          <RedoOutlined />
        </button>
      </Tooltip>
    </Panel>
  );
};


// 将B+树转换为React Flow数据
const convertBPlusTreeToFlowData = (algorithm: BPlusTreeAlgorithm, order: number): { nodes: Node<BPlusNodeData>[], edges: Edge[] } => {
  const allNodes = algorithm.getAllNodes();
  const reactFlowNodes: Node<BPlusNodeData>[] = [];
  const reactFlowEdges: Edge[] = [];

  if (allNodes.length === 0) {
    return { nodes: reactFlowNodes, edges: reactFlowEdges };
  }

  allNodes.forEach(node => {
    // 使用节点自身的level属性，如果没有则计算
    let level = node.level;
    if (level === undefined) {
      level = 0;
      let current = node;
      while (current.parent) {
        level++;
        current = current.parent;
      }
    }

    // 准备keys数组，确保长度为order-1
    const nodeKeys = node.keys.slice(0, node.numKeys);
    const paddedKeys = [...nodeKeys, ...Array(Math.max(0, order - 1 - nodeKeys.length)).fill(null)];

    // 准备pointers数组
    let paddedPointers: (string | null)[];
    if (node.isLeaf) {
      // 叶子节点不需要指针
      paddedPointers = Array(order).fill(null);
    } else {
      // 内部节点需要填充子节点的graphicID
      paddedPointers = Array(order).fill(null);
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach((child, index) => {
          if (child && child.graphicID && index < order) {
            paddedPointers[index] = child.graphicID;
          }
        });
      }
    }

    reactFlowNodes.push({
      id: node.graphicID,
      type: node.isLeaf ? 'bPlusLeafNode' : 'bPlusInternalNode',
      position: { x: 0, y: 0 },
      data: {
        keys: paddedKeys,
        pointers: paddedPointers,
        isLeaf: node.isLeaf,
        level: level,
        order: order,
        next: node.next?.graphicID || null
      }
    });

    // 创建父子关系的边 - 添加严格验证
    if (!node.isLeaf && node.children && Array.isArray(node.children)) {
      node.children.forEach((child, index) => {
        if (child &&
            child.graphicID &&
            node.graphicID &&
            index <= node.numKeys &&
            typeof child.graphicID === 'string' &&
            typeof node.graphicID === 'string' &&
            child.graphicID.trim() !== '' &&
            node.graphicID.trim() !== '') {

          const edgeId = `${node.graphicID}-${child.graphicID}`;
          const sourceHandle = `pointer-${index}`;

          // 确保边ID唯一且有效
          if (!reactFlowEdges.some(edge => edge.id === edgeId)) {
            reactFlowEdges.push({
              id: edgeId,
              source: node.graphicID,
              target: child.graphicID,
              sourceHandle: sourceHandle,
              targetHandle: 'top',
              type: 'straight',
              animated: false,
              markerEnd: {
                type: MarkerType.ArrowClosed,
                width: 15,
                height: 15,
              },
            });
          }
        }
      });
    }

    // 叶子节点的兄弟指针 - 添加严格验证
    if (node.isLeaf &&
        node.next &&
        node.next.graphicID &&
        node.graphicID &&
        typeof node.next.graphicID === 'string' &&
        typeof node.graphicID === 'string' &&
        node.next.graphicID.trim() !== '' &&
        node.graphicID.trim() !== '') {

      const edgeId = `${node.graphicID}-next-${node.next.graphicID}`;

      // 确保边ID唯一且有效
      if (!reactFlowEdges.some(edge => edge.id === edgeId)) {
        reactFlowEdges.push({
          id: edgeId,
          source: node.graphicID,
          target: node.next.graphicID,
          sourceHandle: 'sibling',
          targetHandle: 'sibling-target',
          type: 'straight',
          animated: false,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 15,
            height: 15,
          },
          style: { stroke: 'var(--secondary-text)' }
        });
      }
    }
  });

  return { nodes: reactFlowNodes, edges: reactFlowEdges };
};

// 暴露给外部的操作接口
export interface BPlusTreeVisualizerRef {
  insertKey: (key: number) => Promise<void>;
  deleteKey: (key: number) => Promise<void>;
  resetTree: () => Promise<void>;
  getAllKeys: () => number[];
  setOrder: (order: number) => void;
  setAnimationEnabled: (enabled: boolean) => void;

  // 新增：命令式动画接口，用于外部控件调用
  runInsertAnimation: (key: number) => Promise<void>;
  runDeleteAnimation: (key: number) => Promise<void>;

  // 新增：直接播放命令序列的接口，用于父组件控制
  playCommands: (commands: BPlusCommand[], operation?: 'insert' | 'delete' | 'reset', operationKey?: number) => Promise<void>;
}

interface BPlusTreeVisualizerNewProps {
  initialKeys: (number | string)[];
  order: number;

  // 新增：用于历史回溯的外部状态
  externalNodes?: Node<BPlusNodeData>[];
  externalEdges?: Edge[];

  // 新增：状态变更回调
  onStateChange?: (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => void;
}

// 设置接口
interface Settings {
  isAnimationEnabled: boolean;
  animationSpeed: number;
  order: number;
}

const BPlusTreeVisualizer = forwardRef<BPlusTreeVisualizerRef, BPlusTreeVisualizerNewProps>(({
  initialKeys,
  order,
  externalNodes,
  externalEdges,
  onStateChange
}, ref) => {
  // 状态管理
  const [settings, setSettings] = useState<Settings>({
    isAnimationEnabled: true,
    animationSpeed: 500,
    order: order
  });


  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [storage, setStorage] = useState<BPlusTreeStorage | null>(null);

  // Material-UI消息状态
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // React Flow状态
  const [nodes, setNodes, onNodesChange] = useNodesState<Node<BPlusNodeData>>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);

  // 动画系统状态
  const [animationState, setAnimationState] = useState<AnimationState>({
    currentStep: 0,
    totalSteps: 0,
    isPlaying: false,
    isPaused: false,
    speed: 500
  });

  // Material-UI消息处理函数
  const showMessage = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  // 用 useRef 持久化核心实例
  const bPlusTreeAlgorithmRef = useRef<BPlusTreeAlgorithm | null>(null);
  const animationManagerRef = useRef<AnimationManager | null>(null);
  const commandExecutorRef = useRef<CommandExecutor | null>(null);

  // 从外部节点重建算法状态的辅助函数
  const rebuildAlgorithmFromNodes = useCallback((nodes: Node<BPlusNodeData>[]) => {
    if (!bPlusTreeAlgorithmRef.current) return;

    // 清空当前算法状态
    bPlusTreeAlgorithmRef.current.clear();

    // 从节点数据中提取所有键值
    const keys: number[] = [];
    nodes.forEach(node => {
      if (node.data.keys) {
        node.data.keys.forEach(key => {
          if (typeof key === 'number') {
            keys.push(key);
          }
        });
      }
    });

    // 去重并排序
    const uniqueKeys = [...new Set(keys)].sort((a, b) => a - b);

    // 重新插入所有键值来重建树结构
    uniqueKeys.forEach(key => {
      try {
        bPlusTreeAlgorithmRef.current!.insertElement(key);
      } catch (error) {
        console.warn(`Failed to rebuild key ${key}:`, error);
      }
    });
  }, []);

  // 监听外部状态变化并同步到内部状态
  useEffect(() => {
    if (externalNodes && externalEdges) {
      setNodes(externalNodes);
      setEdges(externalEdges);
      // 从外部节点重建算法状态
      rebuildAlgorithmFromNodes(externalNodes);
    }
  }, [externalNodes, externalEdges, rebuildAlgorithmFromNodes]);

  // 初始化核心实例，只在 order 变化时重建
  useEffect(() => {
    bPlusTreeAlgorithmRef.current = new BPlusTreeAlgorithm(order);
    animationManagerRef.current = new AnimationManager();
    commandExecutorRef.current = new CommandExecutor({
      setNodes,
      setEdges,
      showMessage
    });

    // 如果有外部状态，优先使用外部状态
    if (externalNodes && externalEdges) {
      setNodes(externalNodes);
      setEdges(externalEdges);
      // 从外部节点重建算法状态
      rebuildAlgorithmFromNodes(externalNodes);
    } else {
      // 否则显示空树
      setTimeout(() => {
        if (bPlusTreeAlgorithmRef.current) {
          const { nodes: newNodes, edges: newEdges } = convertBPlusTreeToFlowData(
            bPlusTreeAlgorithmRef.current,
            order
          );
          const layoutedNewNodes = layoutNodes(newNodes, newEdges);
          setNodes(layoutedNewNodes);
          setEdges(newEdges);
        }
      }, 0);
    }
  }, [order, externalNodes, externalEdges, setNodes, setEdges, showMessage]);

  // 动画管理器回调
  useEffect(() => {
    if (!animationManagerRef.current || !commandExecutorRef.current) return;
    animationManagerRef.current.setCallbacks({
      onStepChange: async (step, command) => {
        if (command) {
          await commandExecutorRef.current!.executeCommand(command);
        }
      },
      onStateChange: (state) => {
        setAnimationState(state);
        setIsAnimating(state.isPlaying);
      },
      onComplete: () => {
        showMessage('动画播放完成', 'success');
        setIsAnimating(false);
      },
      onError: (error) => {
        showMessage(`动画执行错误: ${error.message}`, 'error');
        setIsAnimating(false);
      }
    });
    animationManagerRef.current.setSpeed(settings.animationSpeed);
  }, [settings.animationSpeed, showMessage]);

  // 初始化存储
  useEffect(() => {
    const initStorage = async () => {
      try {
        const storageInstance = await getBPlusTreeStorage();
        setStorage(storageInstance);

        // 尝试加载自动保存的数据
        const autoSaveData = await storageInstance.loadAutoSave();
        if (autoSaveData && autoSaveData.keys.length > 0) {
          // 如果有自动保存的数据且没有初始键值，则使用自动保存的数据
          if (initialKeys.length === 0) {
            // 这里可以考虑询问用户是否要恢复自动保存的数据
            console.log('Found auto-saved data:', autoSaveData);
          }
        }
      } catch (error) {
        console.error('Failed to initialize storage:', error);
        showMessage('存储初始化失败', 'warning');
      }
    };

    initStorage();
  }, [initialKeys.length, showMessage]);

  // 更新视图函数 - 移除setNodes和setEdges依赖避免无限循环
  const updateView = useCallback((operation?: 'insert' | 'delete' | 'reset', operationKey?: number) => {
    if (!bPlusTreeAlgorithmRef.current) return;

    try {
      const { nodes: newNodes, edges: newEdges } = convertBPlusTreeToFlowData(
        bPlusTreeAlgorithmRef.current,
        settings.order
      );

      // 验证节点和边数据的有效性
      const validNodes = newNodes.filter(node =>
        node.id &&
        typeof node.id === 'string' &&
        node.id.trim() !== ''
      );

      const validEdges = newEdges.filter(edge =>
        edge.id &&
        edge.source &&
        edge.target &&
        typeof edge.id === 'string' &&
        typeof edge.source === 'string' &&
        typeof edge.target === 'string' &&
        edge.id.trim() !== '' &&
        edge.source.trim() !== '' &&
        edge.target.trim() !== ''
      );

      const layoutedNewNodes = layoutNodes(validNodes, validEdges);
      setNodes(layoutedNewNodes);
      setEdges(validEdges);

      // 触发状态变更通知
      if (onStateChange) {
        const keys = bPlusTreeAlgorithmRef.current.getAllKeys();
        onStateChange({
          nodes: layoutedNewNodes,
          edges: validEdges,
          keys,
          operation,
          operationKey
        });
      }
    } catch (error) {
      console.error('Error updating view:', error);
      showMessage('视图更新失败', 'error');
    }
  }, [settings.order, showMessage, onStateChange]);

  // 自动保存函数
  const autoSave = useCallback(async () => {
    if (!storage || !bPlusTreeAlgorithmRef.current) return;

    try {
      const keys = bPlusTreeAlgorithmRef.current.getAllKeys();
      await storage.autoSave(settings.order, keys);
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [storage, settings.order]);

  // 初始化B+树
  useEffect(() => {
    const initializeTree = async () => {
      if (!bPlusTreeAlgorithmRef.current) return;

      // 清空树
      bPlusTreeAlgorithmRef.current.clear();
      commandExecutorRef.current?.reset();

      // 插入初始键值
      for (const key of initialKeys) {
        if (typeof key === 'number') {
          const commands = bPlusTreeAlgorithmRef.current.insertElement(key);
          if (!settings.isAnimationEnabled) {
            await commandExecutorRef.current!.executeCommands(commands);
          }
        }
      }

      // 总是更新视图，即使没有初始键值
      updateView();
    };

    // 延迟执行以确保所有引用都已初始化
    setTimeout(() => {
      initializeTree();
    }, 100);
  }, [initialKeys, order, settings.isAnimationEnabled, updateView]);







  // 重置处理函数
  const handleReset = async () => {
    animationManagerRef.current!.stop();
    commandExecutorRef.current!.reset();
    bPlusTreeAlgorithmRef.current!.clear();
    updateView('reset'); // 更新视图
    autoSave(); // 自动保存
    showMessage('B+树已重置', 'info');

    // 触发状态变更回调
    if (onStateChange) {
      onStateChange({
        nodes,
        edges,
        keys: [],
        operation: 'reset'
      });
    }
  };

  // 动画控制函数 - 适配XyFlow
  const handlePlay = async () => {
    if (!animationManagerRef.current) return;

    setIsAnimating(true);
    animationManagerRef.current.setCallbacks({
      onStepChange: async (_step, command) => {
        if (command) {
          await commandExecutorRef.current!.executeCommand(command);
        }
      },
      onComplete: () => {
        setIsAnimating(false);
        updateView();
      },
      onError: (error) => {
        setIsAnimating(false);
        showMessage(error.message, 'error');
      }
    });

    await animationManagerRef.current.playAll();
  };

  const handlePause = () => {
    animationManagerRef.current?.pause();
  };

  const handleStop = () => {
    animationManagerRef.current?.stop();
    setIsAnimating(false);
    updateView();
  };

  const handleStepForward = async () => {
    if (!animationManagerRef.current) return;

    await animationManagerRef.current.stepForward();
    updateView();
  };

  const handleStepBackward = async () => {
    if (!animationManagerRef.current) return;

    await animationManagerRef.current.stepBackward();
    updateView();
  };

  const handleJumpToStep = async (step: number) => {
    if (!animationManagerRef.current) return;

    await animationManagerRef.current.jumpToStep(step);
    updateView();
  };

  const handleSpeedChange = (speed: number) => {
    animationManagerRef.current?.setSpeed(speed);
    setSettings(prev => ({ ...prev, animationSpeed: speed }));
  };

  const handleAnimationReset = () => {
    animationManagerRef.current?.reset();
    setIsAnimating(false);
    updateView();
  };

  const handleJumpToNextBreakpoint = async () => {
    if (!animationManagerRef.current) return;

    await animationManagerRef.current.jumpToNextBreakpoint();
    updateView();
  };

  const handleJumpToPreviousBreakpoint = async () => {
    if (!animationManagerRef.current) return;

    await animationManagerRef.current.jumpToPreviousBreakpoint();
    updateView();
  };





  // 获取断点位置
  const breakpoints = animationManagerRef.current?.getStepBreakpoints() ?? [];

  // Snackbar关闭处理函数
  const handleSnackbarClose = useCallback((_event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // 内部插入函数，用于外部调用
  const insertKeyInternal = useCallback(async (key: number) => {
    if (!bPlusTreeAlgorithmRef.current || !animationManagerRef.current || !commandExecutorRef.current) return;

    try {
      if (bPlusTreeAlgorithmRef.current.find(key)) {
        throw new Error(`键值 ${key} 已存在于树中`);
      }

      const commands = bPlusTreeAlgorithmRef.current.insertElement(key);

      if (settings.isAnimationEnabled) {
        animationManagerRef.current.loadCommands(commands);
        await animationManagerRef.current.playAll();
      } else {
        await commandExecutorRef.current.executeCommands(commands);
      }

      updateView();
      showMessage(`成功插入键值 ${key}`, 'success');

      // 触发状态变更回调
      if (onStateChange) {
        const keys = bPlusTreeAlgorithmRef.current.getAllKeys();
        onStateChange({
          nodes,
          edges,
          keys,
          operation: 'insert',
          operationKey: key
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '插入操作失败';
      showMessage(errorMessage, 'error');
      throw error;
    }
  }, [settings.isAnimationEnabled, nodes, edges, onStateChange, showMessage]);

  // 内部删除函数，用于外部调用
  const deleteKeyInternal = useCallback(async (key: number) => {
    if (!bPlusTreeAlgorithmRef.current || !animationManagerRef.current || !commandExecutorRef.current) return;

    try {
      if (!bPlusTreeAlgorithmRef.current.find(key)) {
        throw new Error(`键值 ${key} 不存在于树中`);
      }

      const commands = bPlusTreeAlgorithmRef.current.deleteElement(key);

      if (settings.isAnimationEnabled) {
        animationManagerRef.current.loadCommands(commands);
        await animationManagerRef.current.playAll();
      } else {
        await commandExecutorRef.current.executeCommands(commands);
      }

      updateView();
      showMessage(`成功删除键值 ${key}`, 'success');

      // 触发状态变更回调
      if (onStateChange) {
        const keys = bPlusTreeAlgorithmRef.current.getAllKeys();
        onStateChange({
          nodes,
          edges,
          keys,
          operation: 'delete',
          operationKey: key
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除操作失败';
      showMessage(errorMessage, 'error');
      throw error;
    }
  }, [settings.isAnimationEnabled, nodes, edges, onStateChange, showMessage]);

  // 专用于外部控件的命令式动画方法
  const runInsertAnimationInternal = useCallback(async (key: number): Promise<void> => {
    if (!bPlusTreeAlgorithmRef.current || !animationManagerRef.current || !commandExecutorRef.current) {
      throw new Error('B+ 树组件未正确初始化');
    }

    try {
      if (bPlusTreeAlgorithmRef.current.find(key)) {
        throw new Error(`键值 ${key} 已存在于树中`);
      }

      const commands = bPlusTreeAlgorithmRef.current.insertElement(key);

      if (settings.isAnimationEnabled) {
        animationManagerRef.current.loadCommands(commands);
        await animationManagerRef.current.playAll();
      } else {
        await commandExecutorRef.current.executeCommands(commands);
      }

      updateView('insert', key);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '插入操作失败';
      showMessage(errorMessage, 'error');
      throw error;
    }
  }, [settings.isAnimationEnabled, showMessage, updateView]);

  const runDeleteAnimationInternal = useCallback(async (key: number): Promise<void> => {
    if (!bPlusTreeAlgorithmRef.current || !animationManagerRef.current || !commandExecutorRef.current) {
      throw new Error('B+ 树组件未正确初始化');
    }

    try {
      if (!bPlusTreeAlgorithmRef.current.find(key)) {
        throw new Error(`键值 ${key} 不存在于树中`);
      }

      const commands = bPlusTreeAlgorithmRef.current.deleteElement(key);

      if (settings.isAnimationEnabled) {
        animationManagerRef.current.loadCommands(commands);
        await animationManagerRef.current.playAll();
      } else {
        await commandExecutorRef.current.executeCommands(commands);
      }

      updateView('delete', key);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除操作失败';
      showMessage(errorMessage, 'error');
      throw error;
    }
  }, [settings.isAnimationEnabled, showMessage, updateView]);

  // 专用于外部控件的命令播放方法
  const playCommandsInternal = useCallback(async (commands: BPlusCommand[], operation?: 'insert' | 'delete' | 'reset', operationKey?: number): Promise<void> => {
    if (!animationManagerRef.current || !commandExecutorRef.current) {
      throw new Error('B+ 树组件未正确初始化');
    }

    try {
      if (settings.isAnimationEnabled) {
        animationManagerRef.current.loadCommands(commands);
        await animationManagerRef.current.playAll();
      } else {
        await commandExecutorRef.current.executeCommands(commands);
      }

      updateView(operation, operationKey);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '命令执行失败';
      showMessage(errorMessage, 'error');
      throw error;
    }
  }, [settings.isAnimationEnabled, showMessage, updateView]);

  // 暴露操作接口给外部组件
  useImperativeHandle(ref, () => ({
    insertKey: insertKeyInternal,
    deleteKey: deleteKeyInternal,
    resetTree: handleReset,
    getAllKeys: () => bPlusTreeAlgorithmRef.current?.getAllKeys() || [],
    setOrder: (order: number) => {
      setSettings(prev => ({ ...prev, order }));
    },
    setAnimationEnabled: (enabled: boolean) => {
      setSettings(prev => ({ ...prev, isAnimationEnabled: enabled }));
    },
    // 新增：命令式动画接口
    runInsertAnimation: runInsertAnimationInternal,
    runDeleteAnimation: runDeleteAnimationInternal,
    // 新增：直接播放命令序列的接口
    playCommands: playCommandsInternal
  }), [insertKeyInternal, deleteKeyInternal, runInsertAnimationInternal, runDeleteAnimationInternal, playCommandsInternal]);

  return (
    <ReactFlowProvider>
      <Box
        sx={{
          height: "100%",
          width: "100%",
          position: "relative",
          display: "flex",
          flexDirection: "column",
          flex: 1,
          minHeight: 0, // 关键，防止子元素撑破
        }}
      >
        

        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{
            padding: 0.2,
            includeHiddenNodes: false,
          }}
          minZoom={0.1}
          maxZoom={2}
          defaultViewport={{ x: 0, y: 0, zoom: 1 }}
          style={{ width: "100%", height: "100%" }}
        >
          <CustomZoomControls onReset={handleReset} />
          <Background variant={BackgroundVariant.Dots} gap={20} size={1} />
        </ReactFlow>
      </Box>

      {/* Snackbar 消息 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        onClose={handleSnackbarClose}
        sx={{ ml: 5 }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </ReactFlowProvider>
  );
});

BPlusTreeVisualizer.displayName = 'BPlusTreeVisualizer';

export default BPlusTreeVisualizer;
